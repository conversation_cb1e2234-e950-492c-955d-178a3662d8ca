package com.kikitrade.activity.service.goods.impl;

import com.kikitrade.activity.api.model.response.GoodsDetailResponse;
import com.kikitrade.activity.dal.tablestore.builder.GiftPackConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.goods.GiftPackService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 礼包处理服务实现类
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
@Slf4j
@Service
public class GiftPackServiceImpl implements GiftPackService {

    @Resource
    private GiftPackConfigBuilder giftPackConfigBuilder;

    /**
     * 礼包规则类型常量
     */
    private static final String RULE_TYPE_FIXED_ITEM = "FIXED_ITEM";
    private static final String RULE_TYPE_RANDOM_POOL_PICK = "RANDOM_POOL_PICK";

    @Override
    public List<GoodsDetailResponse.GiftPackItem> getGiftPackItems(String packId, String saasId) {
        log.debug("获取礼包物品信息: packId={}, saasId={}", packId, saasId);
        
        try {
            // 查询礼包配置
            List<GiftPackConfig> configs = giftPackConfigBuilder.findByPackId(packId, saasId);
            
            if (CollectionUtils.isEmpty(configs)) {
                log.warn("未找到礼包配置: packId={}, saasId={}", packId, saasId);
                return new ArrayList<>();
            }

            List<GoodsDetailResponse.GiftPackItem> items = new ArrayList<>();
            
            // 处理每个配置规则
            for (GiftPackConfig config : configs) {
                if (!Boolean.TRUE.equals(config.getIsActive())) {
                    continue; // 跳过未激活的配置
                }
                
                if (RULE_TYPE_FIXED_ITEM.equals(config.getRuleType())) {
                    // 处理固定物品
                    GoodsDetailResponse.GiftPackItem item = processFixedItem(config);
                    if (item != null) {
                        items.add(item);
                    }
                } else if (RULE_TYPE_RANDOM_POOL_PICK.equals(config.getRuleType())) {
                    // 处理随机池物品（这里只是展示可能的物品，实际发放时才确定）
                    List<GoodsDetailResponse.GiftPackItem> randomItems = processRandomPoolItems(config);
                    items.addAll(randomItems);
                }
            }
            
            // 按规则顺序排序
            items.sort(Comparator.comparing(item -> 
                configs.stream()
                    .filter(config -> Objects.equals(config.getItemId(), item.getItemId()))
                    .findFirst()
                    .map(GiftPackConfig::getRuleOrder)
                    .orElse(Integer.MAX_VALUE)
            ));
            
            log.debug("礼包物品信息获取完成: packId={}, itemCount={}", packId, items.size());
            return items;
            
        } catch (Exception e) {
            log.error("获取礼包物品信息异常: packId={}, saasId={}", packId, saasId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, List<GoodsDetailResponse.GiftPackItem>> batchGetGiftPackItems(List<String> packIds, String saasId) {
        log.debug("批量获取礼包物品信息: packIds={}, saasId={}", packIds, saasId);
        
        Map<String, List<GoodsDetailResponse.GiftPackItem>> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(packIds)) {
            return result;
        }
        
        try {
            // 批量查询所有礼包配置
            Map<String, List<GiftPackConfig>> configMap = new HashMap<>();
            for (String packId : packIds) {
                List<GiftPackConfig> configs = giftPackConfigBuilder.findByPackId(packId, saasId);
                if (CollectionUtils.isNotEmpty(configs)) {
                    configMap.put(packId, configs);
                }
            }
            
            // 处理每个礼包的配置
            for (Map.Entry<String, List<GiftPackConfig>> entry : configMap.entrySet()) {
                String packId = entry.getKey();
                List<GiftPackConfig> configs = entry.getValue();
                
                List<GoodsDetailResponse.GiftPackItem> items = new ArrayList<>();
                
                for (GiftPackConfig config : configs) {
                    if (!Boolean.TRUE.equals(config.getIsActive())) {
                        continue;
                    }
                    
                    if (RULE_TYPE_FIXED_ITEM.equals(config.getRuleType())) {
                        GoodsDetailResponse.GiftPackItem item = processFixedItem(config);
                        if (item != null) {
                            items.add(item);
                        }
                    } else if (RULE_TYPE_RANDOM_POOL_PICK.equals(config.getRuleType())) {
                        List<GoodsDetailResponse.GiftPackItem> randomItems = processRandomPoolItems(config);
                        items.addAll(randomItems);
                    }
                }
                
                // 排序
                items.sort(Comparator.comparing(item -> 
                    configs.stream()
                        .filter(config -> Objects.equals(config.getItemId(), item.getItemId()))
                        .findFirst()
                        .map(GiftPackConfig::getRuleOrder)
                        .orElse(Integer.MAX_VALUE)
                ));
                
                result.put(packId, items);
            }
            
            log.debug("批量获取礼包物品信息完成: packCount={}", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("批量获取礼包物品信息异常: packIds={}, saasId={}", packIds, saasId, e);
            return result;
        }
    }

    @Override
    public boolean isGiftPack(String goodsType) {
        return ActivityConstant.GoodsType.GIFT_PACK.getCode().equals(goodsType);
    }

    /**
     * 处理固定物品配置
     */
    private GoodsDetailResponse.GiftPackItem processFixedItem(GiftPackConfig config) {
        try {
            if (config.getItemId() == null) {
                log.warn("固定物品配置缺少物品ID: configId={}", config.getId());
                return null;
            }
            
            GoodsDetailResponse.GiftPackItem item = new GoodsDetailResponse.GiftPackItem();
            item.setItemId(config.getItemId());
            item.setItemType("FIXED");
            item.setItemName("固定物品_" + config.getItemId()); // 实际应该从物品表查询
            
            // 计算数量（取中间值作为展示）
            Integer quantity = calculateDisplayQuantity(config.getQuantityMin(), config.getQuantityMax());
            item.setQuantity(quantity);
            
            item.setDescription("固定发放的物品");
            // item.setIconUrl(); // 实际应该从物品表查询
            
            return item;
            
        } catch (Exception e) {
            log.error("处理固定物品配置异常: configId={}", config.getId(), e);
            return null;
        }
    }

    /**
     * 处理随机池物品配置
     */
    private List<GoodsDetailResponse.GiftPackItem> processRandomPoolItems(GiftPackConfig config) {
        // 这里只是示例实现，实际应该查询随机池配置
        List<GoodsDetailResponse.GiftPackItem> items = new ArrayList<>();
        
        try {
            if (config.getRandomPoolId() == null || config.getPickCount() == null) {
                log.warn("随机池配置不完整: configId={}", config.getId());
                return items;
            }
            
            // 创建一个示例随机物品（实际应该查询随机池表）
            GoodsDetailResponse.GiftPackItem item = new GoodsDetailResponse.GiftPackItem();
            item.setItemId("random_" + config.getRandomPoolId());
            item.setItemType("RANDOM");
            item.setItemName("随机物品池_" + config.getRandomPoolId());
            item.setQuantity(config.getPickCount());
            item.setDescription("从随机池中抽取 " + config.getPickCount() + " 个物品");
            
            items.add(item);
            
        } catch (Exception e) {
            log.error("处理随机池物品配置异常: configId={}", config.getId(), e);
        }
        
        return items;
    }

    /**
     * 计算展示数量
     */
    private Integer calculateDisplayQuantity(Integer min, Integer max) {
        if (min == null && max == null) {
            return 1;
        }
        if (min == null) {
            return max;
        }
        if (max == null) {
            return min;
        }
        if (min.equals(max)) {
            return min;
        }
        // 返回中间值
        return (min + max) / 2;
    }
}
